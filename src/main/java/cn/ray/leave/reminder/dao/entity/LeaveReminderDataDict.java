package cn.ray.leave.reminder.dao.entity;

import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

/**
 * @Title SysDataDict
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/12/17 14:26
 **/
@Table(name = "leave_reminder_data_dict")
@Data
public class LeaveReminderDataDict {

    /**
     * 大厅办管理员手机号
     */
    @Transient
    public static final String CENTER_MANAGE_PHONE = "CENTER_MANAGE_PHONE";

    /**
     *  测试窗口
     */
    @Transient
    public static final String TEST_WIN_NO = "TEST_WIN_NO";

    /**
     *  判断员工返回的时间间隔，单位分钟
     */
    @Transient
    public static final String LEAVE_LIMIT = "LEAVE_LIMIT";

    /**
     *  判断向窗口管理员发送短信的离岗时间，单位分钟
     */
    @Transient
    public static final String SEND_TO_WIN_MANAGE_LIMIT = "SEND_TO_WIN_MANAGE_LIMIT";

    private String cod;

    private String val;
}
