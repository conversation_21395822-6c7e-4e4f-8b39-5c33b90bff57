package cn.ray.leave.reminder.dao.entity;

import jakarta.persistence.Table;

import lombok.Data;

/**
 * @Title AlgoAlarmRecord
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/12/11 19:05
 **/
@Table(name = "algo_alarm_record")
@Data
public class AlgoAlarmRecord {

    private Long id;//int(11) unsigned NOT NULL AUTO_INCREMENT,

    private String cid;//varchar(64) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头id',

    private String status;//tinyint(1) DEFAULT '-1' COMMENT '处理状态：-1，未处理; 1, 已处理；2，已处理（误报）',

    private String createdAt;//time DEFAULT NULL COMMENT '小时段',

    private String snapshotTime;//timestamp NULL DEFAULT NULL COMMENT '抓图时间',

    private String monthTime;//tinyint(4) unsigned DEFAULT NULL COMMENT '月',

    private String dayTime;//date DEFAULT NULL COMMENT '天',

    private String hourTime;//time DEFAULT NULL COMMENT '小时段',

    private String organizationId;//int(11) DEFAULT NULL COMMENT '所属组织id',

    private String workId;//varchar(64) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '工号，人脸识别算法使用',

    private String cameraName;//varchar(60) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头名称，冗余',

    private String cameraAddress;//varchar(255) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头地址，冗余',

    private String deviceGroupId;//int(11) DEFAULT NULL COMMENT '设备组id',

    private String deviceGroupName;//varchar(50) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '设备组名',

    private String fullName;//varchar(50) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '员工姓名',

    private String departmentName;//varchar(250) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '员工姓名',

    private String edgeMiniClusterIp;//varchar(32) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT 'edge-mini k8s的cluster ip',

    private String videoPath;//varchar(128) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '告警录像地址',

    private String videoRecordStatus;//tinyint(2) DEFAULT '0' COMMENT '告警视频录制状态 0-未配置录制任务 1-录制成功 2-录制异常(流异常)',

    private String videoRecordError;//varchar(64) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '视频录制异常原因',

    private String delNo;//varchar(64) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '删除批次号',

}
