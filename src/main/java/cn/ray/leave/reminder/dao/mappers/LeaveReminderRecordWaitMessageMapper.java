package cn.ray.leave.reminder.dao.mappers;

import tk.mybatis.mapper.additional.idlist.DeleteByIdListMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import cn.ray.leave.reminder.dao.entity.LeaveReminderRecordWaitMessage;

/**
 * @Title LeaveReminderRecordWaitMessageMapper
 * <AUTHOR>
 * @Description 通知发送短信日志表
 * @Date 2024/12/12 15:01
 **/
public interface LeaveReminderRecordWaitMessageMapper extends Mapper<LeaveReminderRecordWaitMessage>, InsertListMapper<LeaveReminderRecordWaitMessage>, DeleteByIdListMapper<LeaveReminderRecordWaitMessage, Long> {
}
