package cn.ray.leave.reminder.dao.entity;

import jakarta.persistence.Table;

import lombok.Data;

/**
 * @Title LeaveReminderRecord
 * <AUTHOR>
 * @Description 提醒日志表
 * @Date 2024/12/11 19:05
 **/
@Table(name = "leave_reminder_record")
@Data
public class LeaveReminderRecord {

    private Long id;//int(11) unsigned NOT NULL AUTO_INCREMENT,

    private String cid;//varchar(64) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头id',

    private String createdAt;//time DEFAULT NULL COMMENT '小时段',

    private String cameraName;//varchar(60) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头名称，冗余',

    private String cameraAddress;//varchar(255) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头地址，冗余',

    private String deviceGroupId;//int(11) DEFAULT NULL COMMENT '设备组id',

    private String deviceGroupName;//varchar(50) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '设备组名',

    private String username;

    private String deptId;

    private String workId;//varchar(64) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '工号，人脸识别算法使用',

    private String status;

    private String remark;
}
