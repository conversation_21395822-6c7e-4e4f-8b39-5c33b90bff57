package cn.ray.leave.reminder.dao.entity;

import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * @Title LeaveReminderRecordWaitMessage
 * <AUTHOR>
 * @Description 提醒日志发送短信表
 * @Date 2024/12/11 19:05
 **/
@Table(name = "leave_reminder_record_wait_message")
@Data
public class LeaveReminderRecordWaitMessage {

    @Id
    private Long id;

    private Long createdAt;//time DEFAULT NULL COMMENT '首次创建时间',

    private Long updateAt;//time DEFAULT NULL COMMENT '最后一次更新时间',

    private String username;//varchar(60) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头名称，冗余',

    private String deptId;

    private String workId;//varchar(64) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '工号，人脸识别算法使用',

    private String cameraName;//varchar(60) COLLATE utf8mb4_unicodeCi DEFAULT NULL COMMENT '摄像头名称，冗余',
    /**
     * 1:窗口人员已发送
     * 2:窗口负责人已发送
     */
    private Integer status;
}
