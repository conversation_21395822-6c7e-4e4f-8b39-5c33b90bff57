package cn.ray.leave.reminder.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tk.mybatis.spring.annotation.MapperScan;

import cn.ray.leave.reminder.jdbc.DataSourceHolder;

/**
 * 考勤系统session构造器
 */
@Configuration
@MapperScan(basePackages = "cn.ray.leave.reminder.zhdt.dao", sqlSessionFactoryRef = "zhdtSqlSessionFactory")
public class ZhdtDataSourceConfig {

    @Autowired
    private DataSourceHolder dataSourceHolder;

    @Bean(name = "zhdtSqlSessionFactory")
    public SqlSessionFactory clusterSqlSessionFactory()
            throws Exception {
        DataSource clusterDataSource = dataSourceHolder.getDataSource("zhdt");
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(clusterDataSource);
        return sessionFactory.getObject();
    }

}
