package cn.ray.leave.reminder.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tk.mybatis.spring.annotation.MapperScan;

import cn.ray.leave.reminder.jdbc.DataSourceHolder;

/**
 * 考勤系统session构造器
 */
@Configuration
@MapperScan(basePackages = "cn.ray.leave.reminder.ams.dao", sqlSessionFactoryRef = "amsSqlSessionFactory")
public class AmsDataSourceConfig {

    @Autowired
    private DataSourceHolder dataSourceHolder;

    @Bean(name = "amsSqlSessionFactory")
    public SqlSessionFactory clusterSqlSessionFactory()
            throws Exception {
        DataSource clusterDataSource = dataSourceHolder.getDataSource("ams");
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(clusterDataSource);
        return sessionFactory.getObject();
    }

}
