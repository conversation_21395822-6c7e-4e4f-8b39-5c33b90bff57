package cn.ray.leave.reminder.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Title MessageProperties
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/12/17 14:40
 **/
@ConfigurationProperties(prefix = "msg.template")
@Data
public class MessageTemplateProperties {

    /**
     * 超过阀值不提醒
     */
    private Integer timeout;
    /**
     * 短信发送地址模板
     */
    private String url;

    /**
     * 用户短信模板
     */
    private String user;

    /**
     * 窗口管理员短信模板
     */
    private String windowManager;

    /**
     * 大厅管理员短信模板
     */
    private String centerManager;
}
