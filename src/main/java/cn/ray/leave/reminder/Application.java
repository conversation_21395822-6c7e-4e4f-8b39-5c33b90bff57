package cn.ray.leave.reminder;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

import cn.ray.leave.reminder.jdbc.DataSourceHolder;
import cn.ray.leave.reminder.properties.MessageTemplateProperties;

@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties({DataSourceHolder.class, MessageTemplateProperties.class})
public class Application {

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

}
