package cn.ray.leave.reminder.jdbc;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 从库配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-01-19 上午11:45
 */
@ConfigurationProperties(prefix = "ray.datasource")
public class DataSourceHolder {
    private static Boolean INIT_FLAG = false;

    /**
     * 所有数据源配置
     */
    private Map<String,DataSourceProperties> routes =  new LinkedHashMap();
    /**
     * 所有数据源
     */
    private final Map<String,DataSource> dataSourceMap =  new LinkedHashMap();


    public Map<String, DataSourceProperties> getRoutes() {
        return routes;
    }

    public void setRoutes(Map<String, DataSourceProperties> routes) {
        this.routes = routes;
    }

    private void initDataSource(){
        routes.forEach((key,dsp)->{
            dataSourceMap.put(key,getRoutes().get(key).initializeDataSourceBuilder().build());
        });
        DataSourceHolder.INIT_FLAG = true;
    }

    public DataSource getDataSource(String key) {
        if(!DataSourceHolder.INIT_FLAG)initDataSource();
        DataSource dataSource = dataSourceMap.get(key);
        if(dataSource != null)
            return dataSource;
//        else
//            throw new DataSourceException(DataSourceErrors.DATA_SOURCE_NON);
        return null;
    }

    public JdbcTemplate getJdbcTemplate(String key){
        JdbcTemplate jdbcTemplate = new JdbcTemplate(getDataSource(key));
        return jdbcTemplate;
    }


}
