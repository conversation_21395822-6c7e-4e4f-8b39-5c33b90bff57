package cn.ray.leave.reminder.ams.dao.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Table;

import lombok.Data;

/**
 * @Title AmsLeaveRecordView
 * <AUTHOR>
 * @Description 窗口人员请假
 * @Date 2024/12/16 14:17
 **/
@Table(name = "ams_leave_record_view")
@Data
public class AmsLeaveRecordView {

    private String idCard;

    private String workId;

    @Column(name = "user_name")
    private String username;

    private String beginTime;

    private String endTime;
}
