package cn.ray.leave.reminder.ams.dao.entity;

/**
 * @Title AmsUser
 * <AUTHOR>
 * @Description 用户信息
 * @Date 2024/12/16 14:28
 **/

import jakarta.persistence.Column;
import jakarta.persistence.Table;

import lombok.Data;
@Table(name = "ams_user")
@Data
public class AmsUser {

    private String workId;

    @Column(name="user_name")
    private String username;

    private String idCard;

    private String deptId;

    private String telphone;

    private String deleted;
}
