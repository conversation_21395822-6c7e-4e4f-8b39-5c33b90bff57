package cn.ray.leave.reminder.ams.dao.entity;

/**
 * @Title AmsWindowManagerView
 * <AUTHOR>
 * @Description 窗口负责人
 * @Date 2024/12/16 14:23
 **/


import jakarta.persistence.Column;
import jakarta.persistence.Table;
import lombok.Data;
@Table(name = "ams_window_manager_view")
@Data
public class AmsWindowManagerView {

    private String idCard;

    private String deptId;

    private String workId;

    @Column(name = "user_name")
    private String username;

    private String telphone;
}
