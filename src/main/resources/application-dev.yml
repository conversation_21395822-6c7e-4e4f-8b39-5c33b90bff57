spring:
  datasource:
    name: test
    url: jdbc:mysql://${DB_HOST:127.0.0.1/inference_platform}?useSSL=true&characterEncoding=utf8
    username: ${DB_USER:root}
    password: ${DB_PWD:root}
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # ?????????filters????????sql?????'wall'?????
    filters: stat,wall,slf4j
    # ??connectProperties?????mergeSql????SQL??
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # ????DruidDataSource?????
    useGlobalDataSourceStat: true
    tomcat:
      max-active: 100
      initial-size: 5
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: true
      test-on-return: true
    dbcp2:
      max-open-prepared-statements: 20
ray:
  datasource:
    routes:
      ams:
        name: ams
        url: *******************************************************************************************************************
        username: root
        password: root321
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
#        filters: stat,wall,slf4j
#        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
#        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#        # 合并多个DruidDataSource的监控数据
#        useGlobalDataSourceStat: true
      zhdt:
        name: zhdt
        url: ********************************************************************************************************************
        username: root
        password: Root456987
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
#        filters: stat,wall,slf4j
#        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
#        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#        # 合并多个DruidDataSource的监控数据
#        useGlobalDataSourceStat: true

logging:
  file:
    path: logs/leave-reminder
  level:
    root: info

msg:
  template:
    timeout: 120
